{"translation-revision-date": "2025-07-26 01:28:00+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "vi_VN"}, "%1$s was successfully activated.": ["%1$s đã đ<PERSON><PERSON><PERSON> kích hoạt thành công."], "A plugin was successfully installed and activated.": ["Một plugin đã được cài đặt và kích hoạt thành công."], "%1$s (%2$s) was successfully installed and activated.": ["%1$s (%2$s) đã được cài đặt và kích hoạt thành công."], "A plugin was successfully activated.": ["Một plugin đã đ<PERSON><PERSON><PERSON> kích hoạt thành công."], "Google for WooCommerce": ["Google cho WooCommerce"], "WooPayments": ["WooPayments"], "Omnichannel for WooCommerce": ["<PERSON><PERSON> k<PERSON><PERSON> cho WooCommerce"], "TikTok for WooCommerce": ["TikTok cho WooCommerce"], "Pinterest for WooCommerce": ["Pinterest cho WooCommerce"], "Mercado Pago payments for WooCommerce": ["<PERSON>h toán Mercado Pago cho WooCommerce"], "MailPoet": ["<PERSON><PERSON><PERSON>"], "Could not %(actionType)s %(pluginName)s plugin, %(error)s": ["<PERSON>h<PERSON>ng thể %(actionType)s plugin %(pluginName)s, %(error)s"], "Razorpay": ["Razorpay"], "Creative Mail for WooCommerce": ["Creative Mail cho WooCommerce"], "WooCommerce Shipping & Tax": ["WooCommerce Shipping & Tax"], "WooCommerce Payfast": ["WooCommerce Payfast"], "WooCommerce Stripe": ["WooCommerce Stripe"], "WooCommerce PayPal": ["WooCommerce PayPal"], "WooCommerce ShipStation Gateway": ["Cổng WooCommerce ShipStation"], "There was a problem updating your settings.": ["<PERSON><PERSON> x<PERSON>y ra sự cố khi cập nhật cài đặt của bạn."], "Plugins were successfully installed and activated.": ["Các plugin đã được cài đặt và kích hoạt thành công."], "MM/DD/YYYY": ["MM/DD/YYYY"], "Facebook for WooCommerce": ["Facebook cho WooCommerce"], "Mailchimp for WooCommerce": ["Mailchimp cho WooCommerce"], "Klarna Checkout for WooCommerce": ["<PERSON><PERSON><PERSON> Checkout for WooCommerce"], "Klarna Payments for WooCommerce": ["Klarna Payments for WooCommerce"], "Jetpack": ["Jetpack"]}}, "comment": {"reference": "assets/client/admin/data/index.js"}}