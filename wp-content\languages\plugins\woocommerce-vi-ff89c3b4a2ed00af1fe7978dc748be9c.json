{"translation-revision-date": "2025-07-26 01:28:00+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "vi_VN"}, "Default (<div>)": ["Mặc định (<div>)"], "HTML element": ["Phần tử HTML"], "The %s block requires a product context. When used in a Query Loop, the Query Loop must be configured to display products.": ["Khối %s yêu cầu ngữ cảnh sản phẩm. <PERSON><PERSON> được sử dụng trong Vòng lặp Truy vấn (Query Loop), Vòng lặp Truy vấn phải được cấu hình để hiển thị sản phẩm."], "The <section> element should represent a standalone portion of the document that can't be better represented by another element.": ["Phần tử `<section>` nên đại diện cho một phần độc lập của tài liệu mà không thể được đại diện tốt hơn bởi một phần tử khác."], "The <main> element should be used for the primary content of your document only.": ["Phần tử `<main>` chỉ nên được sử dụng cho nội dung chính của tài liệu của bạn."], "The <footer> element should represent a footer for its nearest sectioning element (e.g.: <section>, <article>, <main> etc.).": ["Phần tử `<footer>` nên đại diện cho một chân trang cho phần tử phân mục gần nhất của nó (ví dụ: <section>, <article>, <main>, v.v.)."], "The <div> element should only be used if the block is a design element with no semantic meaning.": ["Phần tử `<div>` chỉ nên được sử dụng nếu khối là một yếu tố thiết kế không có ý nghĩa ngữ nghĩa."], "The <aside> element should represent a portion of a document whose content is only indirectly related to the document's main content.": ["Phần tử `<aside>` nên đại diện cho một phần của tài liệu có nội dung chỉ liên quan gián tiếp đến nội dung chính của tài liệu."], "The <article> element should represent a self-contained, syndicatable portion of the document.": ["Phần tử `<article>` nên đại diện cho một phần độc lập, có thể phân phối lại của tài liệu."], "The <nav> element should be used to identify groups of links that are intended to be used for website or page content navigation.": ["Phần tử <nav> nên được sử dụng để xác định các nhóm liên kết dùng cho việc điều hướng nội dung trang web hoặc trang."], "The <header> element should represent introductory content, typically a group of introductory or navigational aids.": ["<PERSON>ần tử <header> nên đại diện cho nội dung giới thiệu, thườ<PERSON> là một nhóm các công cụ giới thiệu hoặc điều hướng."], "Product Reviews": ["<PERSON><PERSON><PERSON> gi<PERSON> sản phẩm"]}}, "comment": {"reference": "assets/client/blocks/product-reviews.js"}}