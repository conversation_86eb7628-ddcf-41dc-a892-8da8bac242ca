{"translation-revision-date": "2025-07-26 01:28:00+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=1; plural=0;", "lang": "vi_VN"}, "Classic Cart": ["Giỏ hàng cổ điển"], "Classic Checkout": ["<PERSON><PERSON> <PERSON><PERSON> cổ điển"], "Checkout Cart": ["Giỏ hàng thanh toán"], "You can learn more about the benefits of switching to blocks, compatibility with extensions, and how to switch back to shortcodes <a>in our documentation</a>.": ["Bạn có thể tìm hiểu thêm về lợi ích của việ<PERSON> chuyển sang khối, khả năng tương thích với tiện ích mở rộng và cách chuyển về mã rút gọn <a>trong tài liệu của chúng tôi</a>."], "Cart Shortcode": ["<PERSON>ã rút gọn giỏ hàng"], "Classic Shortcode Placeholder": ["Trình giữ chỗ Shortcode cổ điển"], "Classic shortcode transformed to blocks.": ["Shortcode cổ điển đã được chuyển đổi thành các khối."], "This block will render the classic cart shortcode. You can optionally transform it into blocks for more control over the cart experience.": ["Khối này sẽ hiển thị shortcode giỏ hàng cổ điển. Bạn có thể tùy chọn chuyển đổi nó thành các khối để kiểm soát trải nghiệm giỏ hàng tốt hơn."], "This block will render the classic checkout shortcode. You can optionally transform it into blocks for more control over the checkout experience.": ["Khối này sẽ hiển thị shortcode thanh toán cổ điển. Bạn có thể tùy chọn chuyển đổi nó thành các khối để kiểm soát trải nghiệm thanh toán tốt hơn."], "Renders the classic checkout shortcode.": ["Hiển thị shortcode thanh toán cổ điển."], "Renders the classic cart shortcode.": ["Hiển thị shortcode giỏ hàng cổ điển."], "Transform into blocks": ["<PERSON>y<PERSON>n đổi thành các khối"], "Undo": ["<PERSON><PERSON><PERSON>"], "Learn more": ["<PERSON><PERSON><PERSON> hi<PERSON>u thêm"], "WooCommerce": ["WooCommerce"]}}, "comment": {"reference": "assets/client/blocks/classic-shortcode.js"}}